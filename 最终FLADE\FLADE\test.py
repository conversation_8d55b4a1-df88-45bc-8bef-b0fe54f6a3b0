import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm  # 使用对数规范化
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.ticker as ticker
import math

# 示例数据
# 数据顺序对应算法: ['CELF', 'LIDDE', 'DHHO', 'FLADE', 'TS-VA-MODE', 'PHEE']
data = {
    'k=50': {
        'blog': [10000, 156.17, 96.01, 760.13, 55.06, 84.15],
        'AS733': [10000, 5397.82, 332.11, 6166.76, 324.26, 507.33],
        'CA-HepTh': [10000, 461.50, 133.86, 1768.87, 104.77, 453.58],
        'pgp': [10000, 388.51, 229.13, 6262.83, 92.45, 269.86],
        'NetHEHT': [10000, 416.92, 122.94, 4672.94, 129.39, 411.19],
        'deezer': [10000, 1748.22, 626.65, 26066.09, 1951.21, 1182.19]
    },
    'k=100': {
        'blog': [10000, 159.97, 218.84, 805.19, 128.89, 111.01],
        'AS733': [10000, 1758.87, 592.99, 13061.63, 737.43, 1597.16],
        'CA-HepTh': [10000, 480.45, 277.64, 5715.86, 351.05, 634.61],
        'pgp': [10000, 507.01, 469.42, 8650.74, 182.95, 822.37],
        'NetHEHT': [10000, 545.09, 264.64, 11804.38, 204.37, 921.11],
        'deezer': [10000, 3013.78, 1091.98, 56715.9, 3928.72, 2634.57]
    }
}

algorithms = ['CELF', 'LIDDE',  'DHHO',  'FLADE', 'TS-VA-MODE', 'PHEE']
networks = list(data['k=50'].keys())
k_values = list(data.keys())

fig = plt.figure(figsize=(18, 14))
ax = fig.add_subplot(111, projection='3d')

x_indices = np.arange(len(algorithms))
y_indices = np.arange(len(networks))
x_mesh, y_mesh = np.meshgrid(x_indices, y_indices)

# 为每个算法定义一个固定的颜色（使用更加相近的颜色）
algorithm_colors = {
    'CELF': '#4e79a7',      # 蓝色
    'LIDDE': '#59a14f',     # 绿色
    'PHEE': '#76b7b2',      # 青色
    'DHHO': '#6a6aa9',      # 紫蓝色
    'TS-VA-MODE': '#9c755f', # 棕色
    'FLADE': '#b07aa1'      # 紫红色
}

# 将所有时间值转换为对数形式（log10）
all_times = [math.log10(max(0.1, v)) for k in k_values for net in networks for v in data[k][net]]
# 使用对数规范化（仍然保留，用于颜色透明度）
norm = plt.Normalize(vmin=min(all_times), vmax=max(all_times))

dx = dy = 0.3

# 为图例创建空的柱状图对象
k50_proxy = plt.Rectangle((0, 0), 1, 1, fc="white", alpha=0.8, edgecolor="darkorange", linewidth=2)
k100_proxy = plt.Rectangle((0, 0), 1, 1, fc="white", alpha=0.8, edgecolor="royalblue", linewidth=2)

for k_idx, k in enumerate(k_values):
    offset = (k_idx - (len(k_values) - 1) / 2) * (dx + 0.05)

    # 根据k值选择不同的标记方式
    if k == 'k=50':
        marker_style = "o"  # 圆形标记
        marker_color = "darkorange"
        alpha = 0.9
    else:  # k=100
        marker_style = "s"  # 方形标记
        marker_color = "royalblue"
        alpha = 0.9

    for net_idx, net in enumerate(networks):
        for alg_idx in range(len(algorithms)):
            x = x_indices[alg_idx] + offset
            y = y_indices[net_idx]
            z = 0
            runtime = max(0.1, data[k][net][alg_idx])
            log_runtime = math.log10(runtime)

            # 使用算法对应的固定颜色
            alg_name = algorithms[alg_idx]
            color = algorithm_colors[alg_name]

            # 根据运行时间调整透明度，使运行时间较长的柱子更加突出
            alpha_value = 0.5 + 0.5 * norm(log_runtime)  # 透明度范围从0.5到1.0

            # 使用算法对应的颜色，不添加轮廓
            bar = ax.bar3d(x, y, z, dx, dy, log_runtime, color=color, shade=True, alpha=alpha_value,
                          edgecolor=None)

            # 在柱子顶部添加标记，用于区分k=50和k=100
            if log_runtime > 0.2:  # 只为高度足够的柱子添加标记
                ax.scatter([x + dx/2], [y + dy/2], [log_runtime + 0.05],
                          marker=marker_style, color=marker_color, s=20, alpha=1)

            # 在柱子顶部添加k值标签（仅对第一个网络的第一个算法添加，避免过多标签）
            if net_idx == 0 and alg_idx == 0:
                ax.text(x + dx/2, y + dy/2, log_runtime + 0.2, k,
                        horizontalalignment='center', size=9, color='black', weight='bold')

ax.set_xlabel('Algorithm', fontsize=12, labelpad=15)
ax.set_ylabel('Network', fontsize=12, labelpad=15)
ax.set_zlabel('Runtime (log10 value)', fontsize=12, labelpad=15)

# 设置对数刻度的范围
min_val = 0  # 对数刻度的最小值（log10(1) = 0）
max_val = 5  # 略大于实际最大值log10(56715.9) ≈ 4.75
ax.set_zlim(min_val, max_val)

# 设置对数刻度的刻度位置
major_ticks = [0, 1, 2, 3, 4, 5]  # 对应于 1, 10, 100, 1000, 10000, 100000
ax.zaxis.set_major_locator(ticker.FixedLocator(major_ticks))
ax.zaxis.set_minor_locator(ticker.MultipleLocator(0.2))  # 每个主刻度之间添加次刻度
ax.zaxis.set_minor_formatter(ticker.NullFormatter())

def format_func(x, _):
    # 对数刻度的格式化函数 - 直接显示对数值
    if x == 0:
        return '0'
    elif x == 1:
        return '1'
    elif x == 2:
        return '2'
    elif x == 3:
        return '3'
    elif x == 4:
        return '4'
    elif x == 5:
        return '5'
    else:
        return f'{int(x)}'

ax.zaxis.set_major_formatter(ticker.FuncFormatter(format_func))
ax.tick_params(axis='z', pad=10, labelsize=11)
ax.set_xticks(x_indices)
ax.set_xticklabels(algorithms, rotation=45, ha='right', fontsize=10)
ax.set_yticks(y_indices)
ax.set_yticklabels(networks, fontsize=10)

ax.tick_params(axis='x', pad=8)
ax.tick_params(axis='y', pad=8)
ax.tick_params(axis='z', pad=8)

ax.set_title('Runtime Comparison of Six Algorithms on Six Networks with Different k Values',
             fontsize=14, pad=20)

# 添加注释，解释对数值与实际运行时间的关系
ax.text2D(0.02, 0.98, 'Note: log10 value 3 = 1000s, 4 = 10000s',
         transform=ax.transAxes, fontsize=10, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

# 创建一个自定义的颜色条，显示算法的颜色
from matplotlib.patches import Rectangle

# 创建一个新的坐标轴用于算法颜色图例
algorithm_legend_ax = fig.add_axes([0.15, 0.02, 0.7, 0.05])  # [left, bottom, width, height]
algorithm_legend_ax.axis('off')

# 为每个算法创建一个矩形
alg_patches = []
for i, alg in enumerate(algorithms):
    rect = Rectangle((i/6, 0), 1/6, 1, color=algorithm_colors[alg])
    alg_patches.append(rect)
    algorithm_legend_ax.add_patch(rect)
    # 添加算法名称
    # 根据背景颜色选择文字颜色，确保可读性
    text_color = 'black'
    if alg in ['CELF', 'DHHO', 'TS-VA-MODE']:  # 深色背景使用白色文字
        text_color = 'white'
    algorithm_legend_ax.text((i+0.5)/6, 0.5, alg, ha='center', va='center', fontsize=9,
                            color=text_color)

# 添加标题
# algorithm_legend_ax.text(0.5, 1.5, 'Algorithm Colors', ha='center', va='center', fontsize=10, fontweight='bold')

# 创建一个颜色条来表示运行时间（透明度）
import matplotlib.cm as cm
cmap_alpha = cm.Greys
sm = plt.cm.ScalarMappable(cmap=cmap_alpha, norm=norm)
sm.set_array([])
cbar = fig.colorbar(sm, ax=ax, shrink=0.4, pad=0.1, location='right')
cbar.set_label('Runtime (log10 value)', fontsize=12, labelpad=10)

# 在颜色条上添加一些关键的运行时间标记
for i in range(6):
    value = i
    cbar.ax.text(1.2, value/5, f'{i}', ha='left', va='center', fontsize=8, color='black')

# 创建标记对象用于k值图例
from matplotlib.lines import Line2D
legend_elements = [
    Line2D([0], [0], marker='o', color='w', markerfacecolor='darkorange', markersize=8, label='k=50'),
    Line2D([0], [0], marker='s', color='w', markerfacecolor='royalblue', markersize=8, label='k=100')
]

# 添加图例来标识不同的k值
legend = ax.legend(handles=legend_elements,
          loc='upper right', bbox_to_anchor=(0.95, 0.95), fontsize=10,
          title='Parameter k')
legend.get_title().set_fontsize(10)
legend.get_title().set_fontweight('bold')

ax.view_init(elev=30, azim=45)
plt.subplots_adjust(left=0.05, right=0.95, bottom=0.05, top=0.95)
plt.show()
