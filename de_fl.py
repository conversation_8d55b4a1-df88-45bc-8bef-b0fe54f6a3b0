import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
import time
import pandas as pd
import openpyxl
from collections import defaultdict
from scipy import stats
from datetime import datetime
from base_fun import IC, gen_graph, EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history
from evolution_trajectory import plot_evolution_on_landscape
from plot import plot_fitness_and_kurtosis, plot_kurtosis
from output import export_replacement_data_to_excel, export_evolution_stats_to_excel
from fl_basefun import (edv_cache, node_score_cache, get_node_score, 
                       prune_cache, calculate_distance, optimized_local_search,  calculate_kurtosis, 
                        calculate_kurtosis_threshold)

# 缓存版本的EDV，避免重复计算
def cached_edv(G, seed_set, p, k=None):
    """缓存版本的EDV，避免重复计算"""
    key = (frozenset(seed_set), p)
    if key not in edv_cache:
        edv_cache[key] = EDV(G, seed_set, p)
    return edv_cache[key]

# 初始化所有节点的评分
def initialize_node_scores(G, p):
    """计算并缓存所有节点的度中心性评分"""
    print("正在初始化所有节点度中心性评分...", end="")
    
    # 直接使用NetworkX的度中心性计算函数
    degree_scores = nx.degree_centrality(G)
    
    # 更新全局缓存
    node_score_cache.update(degree_scores)
    print(f" 完成，共计算了 {len(degree_scores)} 个节点的度中心性")

# 个体停滞时的智能替换策略
def intelligent_node_replacement(G, individual, best_solution, p):
    """
    对陷入停滞状态的个体进行智能节点替换，并对新添加的节点进行一阶邻域搜索优化
    
    返回:
    new_individual: 替换后的新个体
    m: 替换的节点数量
    baseline_fitness: 新个体的适应度值
    """
    global node_score_cache, edv_cache
    
    ind_set = set(individual)
    best_set = set(best_solution)
    hamming_distance = len(ind_set.symmetric_difference(best_set)) / 2 #对称差集的一半为单个集合差异节点个数
    # print(f"ind_set: {ind_set}, \n best_set: {best_set}, \n hamming_distance: {hamming_distance}")
    # 确保替换汉明距离大小个节点，但至少替换1个，避免无效替换
    m = max(1, int(hamming_distance))  
    k = len(best_set)
    if m == k:
        m = k - 1 #最多替换k-1个节点，避免替换为度中心性

    # 创建新个体的副本
    new_individual = individual.copy()
    
    # 找出个体中贡献度最低的m个节点
    node_contributions = []
    for node in individual:
        # 计算节点贡献度 - 通过移除该节点观察影响
        temp_set = ind_set - {node}
        if temp_set:  # 确保不是空集
            removed_fitness = cached_edv(G, list(temp_set), p)
            current_fitness = cached_edv(G, list(ind_set), p)
            contribution = current_fitness - removed_fitness
            node_contributions.append((node, contribution))
        else:
            # 如果只有一个节点，则使用节点自身评分
            node_contributions.append((node, node_score_cache.get(node, 0)))
    
    # # 直接使用节点评分作为贡献度
    # for node in individual:
    #     node_contributions.append((node, node_score_cache.get(node, 0)))

    # 按贡献度从低到高排序
    node_contributions.sort(key=lambda x: x[1])
    lowest_contributing_nodes = [node for node, _ in node_contributions[:m]]
    
    
    # 找出不在个体中评分最高的节点
    candidates = [(node, node_score_cache.get(node, 0)) 
                 for node in G.nodes() if node not in ind_set]
    candidates.sort(key=lambda x: x[1], reverse=True)  # 按评分从高到低排序
    highest_scoring_candidates = [node for node, _ in candidates[:m]]
    
    # 替换节点并跟踪新添加的节点
    newly_added_nodes = []
    actual_replaced_count = 0
    for i in range(min(len(lowest_contributing_nodes), len(highest_scoring_candidates))):
        remove_node = lowest_contributing_nodes[i]
        add_node = highest_scoring_candidates[i]
        
        # 在新个体中进行替换
        new_individual.remove(remove_node)
        new_individual.append(add_node)
        newly_added_nodes.append(add_node)
        actual_replaced_count += 1
    
    # 计算替换后的基准适应度
    baseline_fitness = cached_edv(G, new_individual, p)
    new_individual_set = set(new_individual)
    
    # 二次方向微调，对于新加入的节点探索一阶邻居
    for added_node in newly_added_nodes:
        # 获取该节点的一阶邻居
        neighbors = list(G.neighbors(added_node))
        valid_neighbors = [n for n in neighbors if n not in new_individual_set]
        
        if not valid_neighbors:
            continue  # 如果没有有效邻居，继续下一个节点
        
        # 按节点评分排序邻居节点
        valid_neighbors.sort(key=lambda n: node_score_cache.get(n, 0), reverse=True)
        
        # 尝试用所有邻居替换当前新增节点
        for neighbor in valid_neighbors:  
            # 创建临时解
            temp_solution = new_individual.copy()
            temp_solution.remove(added_node)
            temp_solution.append(neighbor)
            
            # 计算新解的适应度
            temp_fitness = cached_edv(G, temp_solution, p)
            
            # 如果找到更好的解，更新
            if temp_fitness > baseline_fitness:
                new_individual = temp_solution
                new_individual_set = set(new_individual)
                baseline_fitness = temp_fitness
                # print("二次微调成功")
                break  # 找到改进就立即应用并继续下一个节点
    
    return new_individual, actual_replaced_count, baseline_fitness

# 更新个体停滞检测状态，并在检测到停滞时进行智能节点替换
def update_stagnation_detection(i, current_iter, fitness_history, fitness_current, 
                              stagnation_counters, stagnation_states, individuals, best_solution, G, p,
                              stagnation_threshold=0, stagnation_trigger=3, is_generation_best=False):
    """
    更新个体停滞检测状态，并在检测到停滞时进行智能节点替换
    
    返回: (是否从停滞状态中恢复, 是否进行了节点替换, 新个体, 替换信息, 新适应度)
    """
    # 更新适应度历史记录(保持最近10代)
    fitness_history[i].append(fitness_current)
    if len(fitness_history[i]) > 10:
        fitness_history[i] = fitness_history[i][-10:]
    
    recovered = False
    replaced = False
    new_individual = individuals[i].copy()
    replacement_info = None
    new_fitness = fitness_current  # 默认使用当前适应度
    
    # 至少需要两代才能计算变化
    if len(fitness_history[i]) < 2:
        return recovered, replaced, new_individual, replacement_info, new_fitness
    
    # 计算适应度变化
    fitness_change = fitness_history[i][-1] - fitness_history[i][-2]
    abs_change = abs(fitness_change)
    

    # 情况1: 适应度显著提升 - 重置停滞状态
    if fitness_change > stagnation_threshold:
        if stagnation_states[i]:  # 如果之前处于停滞状态
            recovered = True
        stagnation_counters[i] = 0
        stagnation_states[i] = False
        
    # 情况2: 适应度停止变化,可能处于停滞状态
    elif abs_change < stagnation_threshold:
        stagnation_counters[i] += 1
        
        # 连续3代无显著变化，判定为停滞
        if stagnation_counters[i] >= stagnation_trigger:
            # 更新停滞状态标记
            stagnation_states[i] = True
            
            # 对非最优个体执行替换操作
            if not is_generation_best:
                old_individual = individuals[i].copy()
                old_fitness = fitness_current
                new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, individuals[i], best_solution, p)
                
                # 再次计算新个体的实际适应度，确保与缓存一致
                new_fitness = cached_edv(G, new_individual, p)
                
                replaced = True
                stagnation_counters[i] = 0
                
                # 记录替换信息
                replacement_info = {
                    'iteration': current_iter,
                    'individual_idx': i,
                    'old_individual': old_individual,
                    'new_individual': new_individual,
                    'old_fitness': old_fitness,
                    'new_fitness': new_fitness,
                    'num_replaced': num_replaced
                }
    
    return recovered, replaced, new_individual, replacement_info, new_fitness

# 优化的差分变异操作
def Differential_Mutation(G, X, F, pop, k, nodelist, p=0.1, terrain="ridge", best_solution=None):
    global node_score_cache
    nodes_list = list(G.nodes())  # 只创建一次
    M = []  # 变异种群结果容器
    X_sets = [frozenset(x) for x in X]
    
    # 对每个个体执行变异操作
    for i in range(pop):
        # 当前解
        Xcurrent = X[i].copy()
        
        # === 基于地形特征的搜索策略选择 ===
        if terrain == "ridge":
            # 使用random.sample代替多次random.choice，减少随机数生成开销 #DE/rand/1/bin
            rand_indices = random.sample(range(pop), min(3, pop))
            base_vector = X[rand_indices[0]].copy()
            diff_set = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
        else:  
            # "peak" 山峰区#DE/current-to-rand/1/bin 变体
            rand_indices = random.sample(range(pop), min(3, pop))
            base_vector = Xcurrent.copy()
            diff_set1 = X_sets[rand_indices[0]] - frozenset(Xcurrent)
            diff_set2 = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            diff_set = diff_set1.union(diff_set2)
        
        M.append(base_vector)
        N = math.ceil(F * len(diff_set))
        current_set = set(M[i])
        node_to_score = {node: node_score_cache.get(node, 0) for node in current_set}
        
        for _ in range(N):
            if not current_set:
                break
            # 找出最差节点 - 避免每次都遍历整个集合
            worst_node = min(current_set, key=lambda node: node_score_cache.get(node, 0))
            # 转换为列表以加速随机选择
            available_diff = list(diff_set - current_set)
            
            if available_diff:
                replace_node = random.choice(available_diff)
            else:
                # 预先计算可用节点集合
                available_nodes = list(set(nodes_list) - current_set)
                if not available_nodes:
                    continue
                replace_node = random.choice(available_nodes)
            # 执行替换 - 优化异常处理逻辑
            try:
                worst_index = M[i].index(worst_node)
                M[i][worst_index] = replace_node
                current_set.remove(worst_node)
                current_set.add(replace_node)
                # 更新节点评分字典
                node_to_score.pop(worst_node, None)
                node_to_score[replace_node] = node_score_cache.get(replace_node, 0)
            except ValueError:
                # 简化异常处理逻辑
                M[i] = list((current_set - {worst_node}) | {replace_node})
                while len(M[i]) < k:
                    candidate = random.choice(list(set(nodes_list) - set(M[i])))
                    M[i].append(candidate)
                # 重建当前集合
                current_set = set(M[i])
            
    return M

# 优化的交叉操作
def Crossover(X, M, cr, pop, k, nodelist):
    """
    优化版交叉操作
    """
    nodelist_set = set(nodelist)  # 预先创建节点集合以加速操作
    real_C = []
    
    # 获取节点评分（如果存在）
    node_scores = getattr(Differential_Mutation, 'node_scores', {})
    
    # 预生成随机数列表，减少函数调用开销
    random_values = [random.random() for _ in range(pop * k)]
    
    for i in range(pop):
        # 创建跟踪集合和结果列表
        added_nodes = set()
        C = []
        
        for j in range(k):
            ran = random_values[i * k + j]
            
            # 简化逻辑判断，减少重复计算
            use_m = (ran < cr and M[i][j] not in added_nodes)
            use_x = not use_m and X[i][j] not in added_nodes
            
            if use_m:
                temp = M[i][j]
            elif use_x:
                temp = X[i][j]
            else:
                # 冲突处理 - 直接从未添加节点中选择
                available = nodelist_set - added_nodes
                temp = random.choice(list(available)) if available else random.choice(nodelist)
            
            C.append(temp)
            added_nodes.add(temp)
        
        # 高效补充缺失节点
        if len(C) < k:
            # 使用集合差集直接找出未使用的节点
            available_nodes = list(nodelist_set - added_nodes)
            
            # 如果有节点评分，则使用评分排序
            if node_scores:
                available_nodes.sort(key=lambda n: node_scores.get(n, 0), reverse=True)
            
            # 填充剩余位置
            C.extend(available_nodes[:k-len(C)])
           
        real_C.append(C)
    
    return real_C

# 优化的选择操作
def Selection(G, X, C, pop, p=0.1):
    X_keys = [frozenset(x) for x in X]
    C_keys = [frozenset(c) for c in C]
    solutions_to_evaluate = set(X_keys + C_keys)
    for sol in solutions_to_evaluate:
        if (sol, p) not in edv_cache:
            edv_cache[(sol, p)] = EDV(G, list(sol), p)
    temp_X = []
    for i in range(pop):
        x_fitness = edv_cache[(X_keys[i], p)]
        c_fitness = edv_cache[(C_keys[i], p)]
        
        temp_X.append(X[i] if x_fitness >= c_fitness else C[i])
    
    return temp_X

# 优化的差分进化算法主函数
def de(G, n, k, p, max_iter=50, F=0.6, cr=0.4, kurtosis_threshold=3):
    global node_score_cache
    initialize_node_scores(G, p)

    nodelist = list(G.nodes())
    fitness_history = []
    
    # 地形统计 - 只统计切换为山峰的次数
    terrain_transitions = {"to_peak": 0}
    # 局部搜索统计
    local_search_stats = {"total_attempts": 0, "successful_attempts": 0}
    # 节点替换统计
    replacement_stats = {"total_attempts": 0, "successful_attempts": 0, "exceeded_global_best": 0}
    
    # 修改停滞检测的初始化
    individual_fitness_history = [[] for _ in range(n)]  # 每个个体的适应度历史
    stagnation_detected = [False] * n  # 记录每个个体是否处于停滞状态
    stagnation_counter = [0] * n  # 记录每个个体连续停滞的代数
    
    # 峰度计算相关数据结构 
    kurtosis_stats = []  # 存储每代的峰度值
    kurtosis_history = []  # 记录所有代的峰度值
    
    # 初始化种群
    X = degree_initialization(G, n, k)
    
    # 预计算初始种群适应度
    unique_solutions = {tuple(sorted(ind)) for ind in X}
    for sol in unique_solutions:
        edv_cache[sol] = EDV(G, list(sol), p)
    
    # 获取当前最佳解
    population_fitness = [cached_edv(G, ind, p) for ind in X]
    current_best = max(population_fitness)
    current_best_idx = population_fitness.index(current_best)
    
    best_solution = X[current_best_idx].copy()
    best_fitness = current_best
    fitness_history.append(best_fitness)
    
    # 初始化地形 - 默认设为山脊
    current_terrain = "ridge"
    # 进化控制变量
    last_fitness = best_fitness
    
    # 初始化进化轨迹数据收集
    evolution_data = []
    replacement_data = []  # 新增：记录节点替换数据
    
    # 主进化循环
    for iter in range(max_iter):
        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]
        
        # 计算当前种群的峰度值
        result_unbiased, result_biased = calculate_kurtosis(current_fitness)
        print(f"峰度值 (unbiased): {result_unbiased:.4f}, 峰度值 (biased): {result_biased:.4f}")
        kurtosis_value = result_unbiased

        kurtosis_stats.append((iter+1, kurtosis_value))
        kurtosis_history.append(kurtosis_value)
        
        # 使用计算的峰度阈值替代固定值3
        if kurtosis_value > kurtosis_threshold:
            print(f"第{iter+1}代 | 激活地形感知策略 (峰度值 {kurtosis_value:.4f} > {kurtosis_threshold:.4f})")
            previous_terrain = current_terrain
            
            # 计算所有个体的适应度并找出最优个体
            individual_fitness = [(i, cached_edv(G, ind, p)) for i, ind in enumerate(X)]
            individual_fitness.sort(key=lambda x: x[1], reverse=True)
            best_individual_idx = individual_fitness[0][0]
            best_individual = X[best_individual_idx].copy()
            
            # 对最优个体执行局部搜索
            start_time = time.time()
            optimized = optimized_local_search(best_individual, G, p, k)
            print(f"局部搜索完成，用时: {time.time() - start_time:.2f} 秒")
            original_fitness = cached_edv(G, best_individual, p)
            new_fitness = cached_edv(G, optimized, p)
            
            # 更新局部搜索统计
            local_search_stats["total_attempts"] += 1
            
            # 根据局部搜索结果判断地形
            if new_fitness > original_fitness:
                # 局部搜索有改进，处于山峰区
                local_search_stats["successful_attempts"] += 1
                
                # 只有当从非山峰区切换到山峰区时才记录
                if current_terrain != "peak":
                    terrain_transitions["to_peak"] += 1
                    print(f"地形从{current_terrain}切换为peak")
                
                current_terrain = "peak"
                X[best_individual_idx] = optimized  # 使用优化后的个体替换
                current_fitness[best_individual_idx] = new_fitness  # 更新适应度
            else:
                print(f"^^^^^^^^^^^^^陷入局部最优峰值，执行引导策略^^^^^^^^^^^^^")
                # 无论之前是什么地形，都设置为ridge
                current_terrain = "ridge"
                
                # 执行引导策略,计算每个个体与最优解的汉明距离，对停滞个体执行节点替换
                best_solution_set = set(best_solution)
                for i in range(n):
                    # 跳过最优个体,只对非最优个体执行节点替换
                    if i == best_individual_idx:
                        continue
                    
                    # 检查个体停滞状态
                    # if stagnation_detected[i] and stagnation_counter[i] >= 3:
                    # 执行智能节点替换
                    old_fitness = cached_edv(G, X[i], p)
                    old_individual = X[i].copy()  # 保存替换前的个体
                    new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, X[i], best_solution, p)
                    
                    # 再次计算新个体的实际适应度，确保与缓存一致
                    new_fitness = cached_edv(G, new_individual, p)
                    
                    # 更新节点替换统计
                    replacement_stats["total_attempts"] += 1
                    if new_fitness > old_fitness:
                        replacement_stats["successful_attempts"] += 1
                    
                    # 检查是否超过全局最优解
                    if new_fitness > best_fitness:
                        replacement_stats["exceeded_global_best"] += 1
                    
                    X[i] = new_individual
                    current_fitness[i] = new_fitness  # 更新当前适应度
                    stagnation_counter[i] = 0  # 重置停滞计数器
                    
                    # 记录替换信息
                    replacement_info = { 'iteration': iter+1, 'individual_idx': i, 'old_individual': old_individual,
                        'new_individual': new_individual, 'old_fitness': old_fitness, 'new_fitness': new_fitness,
                        'actual_new_fitness': new_fitness, 'num_replaced': num_replaced, 'is_best_in_generation': False
                    }
                    replacement_data.append(replacement_info)
                # 删除以下注释掉的代码，确保不会意外将地形设置为peak
                # current_terrain = "peak"
        
        else:
            print(f"第{iter+1}代 | 正常进化 (峰度值 {kurtosis_value:.4f} < {kurtosis_threshold:.4f})")
            # 峰度值低于阈值时，始终使用ridge地形
            current_terrain = "ridge"
        
        # 根据当前地形执行差分变异
        M = Differential_Mutation(G, X, F, n, k, nodelist, p=p, terrain=current_terrain)
        C = Crossover(X, M, cr, n, k, nodelist)
        X = Selection(G, X, C, n, p)
       
        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]
        current_best = max(current_fitness)
        best_idx = current_fitness.index(current_best)
        
        # 更新全局最优解
        if current_best > best_fitness:
            best_fitness = current_best
            best_solution = X[best_idx].copy()
        
        # 更新适应度历史
        fitness_history.append(current_best)
        # 更新每个个体的适应度历史并检查停滞
        recovered_count = 0
        # 找出当前代的最优个体索引
        current_best_idx = np.argmax(current_fitness)

        # 更新每个个体的适应度历史并检查停滞
        for i in range(n):
            # 判断当前个体是否为本代最优
            is_best_in_generation = (i == current_best_idx)
            recovered, replaced, new_individual, replacement_info, new_fitness = update_stagnation_detection(
                i, iter+1, individual_fitness_history, current_fitness[i],
                stagnation_counter, stagnation_detected, X, best_solution, G, p,
                stagnation_threshold=1e-6, stagnation_trigger=3, #停滞阈值设为1e-6
                is_generation_best=is_best_in_generation  # 传入是否为当前代最优
            )
            
            # 如果进行了节点替换，更新个体和适应度
            if replaced:
                old_fitness = current_fitness[i]
                X[i] = new_individual
                # 使用函数返回的适应度，不再重新计算
                current_fitness[i] = new_fitness
                
                # 更新节点替换统计
                replacement_stats["total_attempts"] += 1
                if new_fitness > old_fitness:
                    replacement_stats["successful_attempts"] += 1
                
                # 检查是否超过全局最优解
                if new_fitness > best_fitness:
                    replacement_stats["exceeded_global_best"] += 1
                
                if replacement_info:
                    replacement_info['actual_new_fitness'] = new_fitness  # 使用新计算的适应度
                    replacement_info['is_best_in_generation'] = is_best_in_generation
                    # 检查是否超过全局最优解
                    replacement_info['exceeded_global_best'] = new_fitness > best_fitness
                    replacement_data.append(replacement_info)
            
            if recovered:
                recovered_count += 1


        # 更新全局最优解(如果有改进)
        generation_best_idx = np.argmax(current_fitness)
        generation_best = current_fitness[generation_best_idx]

        if generation_best > current_best:
            current_best = generation_best
            best_solution = X[generation_best_idx].copy()
        
        # 在所有替换和进化操作完成后，再收集当前代的数据
        current_iteration_data = {
            'individuals': X.copy(), 
            'fitness': current_fitness.copy(),  # 确保使用最新的适应度值
            'terrain': current_terrain
        }
        evolution_data.append(current_iteration_data)
        
        print(f"当前最佳适应度: {current_best:.4f}, 平均适应度: {np.mean(current_fitness):.4f}")
        print(f"最优个体: {best_solution}")
        print('=============================================================')

    # 计算局部搜索成功率
    local_search_success_rate = 0
    if local_search_stats["total_attempts"] > 0:
        local_search_success_rate = local_search_stats["successful_attempts"] / local_search_stats["total_attempts"] * 100
    
    # 计算节点替换成功率
    replacement_success_rate = 0
    if replacement_stats["total_attempts"] > 0:
        replacement_success_rate = replacement_stats["successful_attempts"] / replacement_stats["total_attempts"] * 100
    
    # 输出局部搜索统计
    print(f"==================局部搜索统计 ==================")
    print(f"总尝试次数: {local_search_stats['total_attempts']}，成功次数: {local_search_stats['successful_attempts']}")
    print(f"成功率: {local_search_success_rate:.2f}%")
    
    # 输出节点替换统计
    print(f"==================节点替换统计==================")
    print(f"总尝试次数: {replacement_stats['total_attempts']}，成功次数: {replacement_stats['successful_attempts']}")
    print(f"成功率: {replacement_success_rate:.2f}%")    
    print(f"超过全局最优次数: {replacement_stats['exceeded_global_best']}")
    
    # 输出地形转换统计
    print(f"==================地形转换统计==================")
    print(f"切换为山峰区次数: {terrain_transitions['to_peak']}次")
    
    # 输出峰度统计摘要
    kurtosis_threshold_count = sum(1 for _, kurt in kurtosis_stats if kurt > kurtosis_threshold)
    print(f"==================峰度统计摘要==================")
    print(f"峰度大于{kurtosis_threshold:.0f}的迭代次数: {kurtosis_threshold_count}/{max_iter} ({kurtosis_threshold_count/max_iter*100:.2f}%)")

    return best_solution, fitness_history, evolution_data, replacement_data, kurtosis_stats

def main():
    
    start_time = time.time()

    # 重置全局缓存
    global edv_cache, node_score_cache 
    edv_cache = {}
    node_score_cache = {}
    
    # network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\email.txt"  
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    network_path = "D:\\VS\\code\\networks\\WS.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"

    G = gen_graph(network_path)
    p = 0.05
    k = 50 # 种子集大小
    NP = 10 # 种群大小
    kurtosis_threshold = 3
    max_iter = 50  # 最大迭代次数
    
    # 设置差分进化算法参数
    F = 0.6  # 变异因子
    cr = 0.4  # 交叉概率
    
    # 修改DE函数调用，传入计算的峰度阈值
    best_seed, fitness_history, evolution_data, replacement_data, kurtosis_stats = (
        de(G, n=NP, k=k, p=p, max_iter=max_iter, F=F, cr=cr, kurtosis_threshold=kurtosis_threshold))
    
    # 导出节点替换数据到Excel
    # excel_file = export_replacement_data_to_excel(replacement_data, network_path, k, p, kurtosis_stats)
    
    # 导出进化统计数据到Excel - 现在调用output模块中的函数
    # stats_excel_file = export_evolution_stats_to_excel(evolution_data, kurtosis_stats, replacement_data, network_path, k, p, max_iter)
    
    # 评估最终结果
    ic_value = IC(G, best_seed, p, mc=1000)
    print(f"==================最终结果==================")
    print(f"Network: {network_path}")  # 直接输出网络文件路径
    print(f"k={k}，p={p},NP={NP}")
    print(f"seed set (size: {len(set(best_seed))}):\n {best_seed}")
    print(f"IC influence: {ic_value:.4f}")
    print(f"running time: {time.time() - start_time:.2f} s")

    #可视化种子集分布
    # visualize_seed_set(G, best_seed, 'seed_set_distribution.png')
    # print(f"seed_set_distribution.png saved")
    
    # 使用新的绘图函数，将适应度历史和峰度值变化合并到一个图表中
    plot_fitness_and_kurtosis(fitness_history, kurtosis_stats, 'fitness_and_kurtosis.png', kurtosis_threshold=kurtosis_threshold)
    
    # 以下注释掉原来分开的绘图函数调用
    # plot_fitness_history(fitness_history, 'fitness_history.png')
    # print(f"fitness_history.png saved")
    # plot_kurtosis(kurtosis_stats, "峰度值变化趋势", kurtosis_threshold=kurtosis_threshold)
    # print(f"峰度值变化趋势.png saved")
    
    # 绘制进化轨迹 - 确保传入正确的save_path参数
    save_path = 'evolution_landscape.pdf'
    print(f"准备保存图像到: {save_path}")
    plot_evolution_on_landscape(evolution_data, interval=1, show_iteration_colorbar=False, 
                               replacement_data=replacement_data, save_path=save_path)

if __name__ == "__main__":
    main()
